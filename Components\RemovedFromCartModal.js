import React, { useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Animated, Dimensions } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

const RemovedFromCartModal = ({
    visible,
    onClose,
    productName,
    productWeight,
    isProductDetailScreen = false,
    autoHide = true,
    autoHideDelay = 2000
}) => {
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const slideAnim = useRef(new Animated.Value(30)).current;
    const scaleAnim = useRef(new Animated.Value(0.8)).current;

    useEffect(() => {
        if (visible) {
            // Animate in with bounce effect (same as AddToCartModal)
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 250,
                    useNativeDriver: true,
                }),
                Animated.spring(slideAnim, {
                    toValue: 0,
                    tension: 100,
                    friction: 8,
                    useNativeDriver: true,
                }),
                Animated.spring(scaleAnim, {
                    toValue: 1,
                    tension: 100,
                    friction: 8,
                    useNativeDriver: true,
                })
            ]).start();

            // Auto hide after delay
            if (autoHide) {
                const timer = setTimeout(() => {
                    animateOut();
                }, autoHideDelay);

                return () => clearTimeout(timer);
            }
        } else {
            animateOut();
        }
    }, [visible]);

    const animateOut = () => {
        // Animate out quickly (same as AddToCartModal)
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 150,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 30,
                duration: 150,
                useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
                toValue: 0.8,
                duration: 150,
                useNativeDriver: true,
            })
        ]).start(() => {
            if (onClose) {
                onClose();
            }
        });
    };

    const handlePress = () => {
        // Close immediately when tapped
        animateOut();
    };

    if (!visible) return null;

    return (
        <View
            style={{
                position: 'absolute',
                bottom: isProductDetailScreen ? 110 : 10, // Same positioning as AddToCartModal
                left: 16,
                right: 16,
                zIndex: 1000,
                pointerEvents: 'box-none' // Allow touches to pass through to background
            }}
        >
            <TouchableOpacity
                activeOpacity={0.9}
                onPress={handlePress}
                style={{
                    opacity: fadeAnim,
                    transform: [
                        { translateY: slideAnim },
                        { scale: scaleAnim }
                    ],
                    pointerEvents: 'auto' // Only the modal card captures touches
                }}
            >
                <Animated.View
                    style={{
                        backgroundColor: '#000', // Same black background as AddToCartModal
                        borderRadius: 8,
                        paddingVertical: 12,
                        paddingHorizontal: 16,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.2,
                        shadowRadius: 6,
                        elevation: 4
                    }}
                >
                    {/* Single Line Layout - matching AddToCartModal */}
                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between'
                    }}>
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            flex: 1
                        }}>
                            <View style={{
                                width: 24,
                                height: 24,
                                borderRadius: 12,
                                backgroundColor: '#EF4444', // Red background for remove icon
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginRight: 8
                            }}>
                                <MaterialIcons
                                    name="remove"
                                    size={14}
                                    color="white"
                                />
                            </View>

                            <Text style={{
                                fontSize: 14,
                                fontWeight: '600',
                                color: 'white'
                            }}>
                                Removed from Cart
                            </Text>
                        </View>

                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center'
                        }}>
                            <Text style={{
                                fontSize: 12,
                                fontWeight: '400',
                                color: '#9CA3AF', // Lighter gray for "Tap to dismiss"
                                marginRight: 4
                            }}>
                                Tap to dismiss
                            </Text>
                            <MaterialIcons
                                name="close"
                                size={14}
                                color="#9CA3AF"
                            />
                        </View>
                    </View>
                </Animated.View>
            </TouchableOpacity>
        </View>
    );
};

export default RemovedFromCartModal;
