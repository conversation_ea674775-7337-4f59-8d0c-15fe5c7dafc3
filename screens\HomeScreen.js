import React, { useState, useEffect, useRef } from "react";
import { View, Text, Image, TouchableOpacity, Dimensions, FlatList, StatusBar, Animated, RefreshControl, Keyboard, Easing } from "react-native";

import { MaterialIcons } from "@expo/vector-icons";

import banner1 from '../assets/banner1.png';
import banner2 from '../assets/banner2.png';
import banner3 from '../assets/banner3.png';
import banner4 from '../assets/banner4.png';
import banner6 from '../assets/banner6.png';
import logo from '../assets/logo.png';
import { useCart } from '../context/CartContext';
import { useUser } from '../context/UserContext';
import { getUserProducts, getBestsellingProducts, clearProductCaches } from '../utils/api/userProductApi';
import { getAllCategories, clearCategoriesCache } from '../utils/api/categoryApi';
import FlashSaleTimer from '../Components/FlashSaleTimer';
import AnimatedSearchBar from '../Components/AnimatedSearchBar';
import AddToCartModal from '../Components/AddToCartModal';


// Skeleton loading components
const SkeletonBox = ({ width, height, style }) => {
    const pulseAnim = useRef(new Animated.Value(0.3)).current;

    useEffect(() => {
        const pulse = Animated.loop(
            Animated.sequence([
                Animated.timing(pulseAnim, {
                    toValue: 1,
                    duration: 800,
                    useNativeDriver: true
                }),
                Animated.timing(pulseAnim, {
                    toValue: 0.3,
                    duration: 800,
                    useNativeDriver: true
                })
            ])
        );
        pulse.start();

        return () => pulse.stop();
    }, []);

    return (
        <Animated.View
            style={[
                {
                    width: width,
                    height: height,
                    backgroundColor: '#E0E0E0',
                    borderRadius: 8,
                    opacity: pulseAnim
                },
                style
            ]}
        />
    );
};

const CategorySkeleton = () => {
    return (
        <View className="items-center mx-3 mt-2">
            <SkeletonBox width={80} height={80} style={{ borderRadius: 40 }} />
            <SkeletonBox width={60} height={16} style={{ marginTop: 8, borderRadius: 4 }} />
        </View>
    );
};

const ProductCardSkeleton = ({ width = 180 }) => {
    return (
        <View className="m-2" style={{ width }}>
            <SkeletonBox width={width} height={120} style={{ borderTopLeftRadius: 12, borderTopRightRadius: 12 }} />
            <View className="p-3 bg-white rounded-b-xl">
                <SkeletonBox width={width - 40} height={18} style={{ marginBottom: 8 }} />
                <SkeletonBox width={60} height={14} style={{ marginBottom: 8 }} />
                <View className="flex-row justify-between items-center">
                    <SkeletonBox width={50} height={16} />
                    <SkeletonBox width={80} height={30} style={{ borderRadius: 6 }} />
                </View>
            </View>
        </View>
    );
};

// Banner Carousel Component
const BannerCarousel = () => {
    const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
    const bannerImages = [banner1, banner2, banner3, banner4, banner6];
    const screenWidth = Dimensions.get('window').width;
    const flatListRef = useRef(null);
    const scrollX = useRef(new Animated.Value(0)).current;
    const viewConfigRef = useRef({ viewAreaCoveragePercentThreshold: 50 });
    const itemWidth = screenWidth - 32; // Account for padding

    // Animation for scale effect
    const scaleAnim = useRef(new Animated.Value(1)).current;

    // Auto slide timer with improved transitions (delayed start for performance)
    useEffect(() => {
        let animationTimer;
        let autoScrollTimer;

        const startAutoSlide = () => {
            // Start scale animation with smoother easing
            Animated.sequence([
                // Scale down slightly with easeOut for smoother start
                Animated.timing(scaleAnim, {
                    toValue: 0.98,
                    duration: 400,
                    easing: Easing.out(Easing.ease),
                    useNativeDriver: true
                }),
                // Scale back up with easeIn for smoother finish
                Animated.timing(scaleAnim, {
                    toValue: 1,
                    duration: 400,
                    easing: Easing.in(Easing.ease),
                    useNativeDriver: true
                })
            ]).start();

            // Delay the scroll to allow the scale animation to complete
            autoScrollTimer = setTimeout(() => {
                // Calculate next index with safety check
                const nextIndex = (currentBannerIndex + 1) % bannerImages.length;

                // Scroll to next banner with smooth animation
                if (flatListRef.current) {
                    flatListRef.current.scrollToIndex({
                        index: nextIndex,
                        animated: true,
                        viewPosition: 0,
                        viewOffset: 0
                    });
                }
            }, 4000); // Change banner every 4 seconds
        };

        // Delay banner auto-slide to not interfere with initial loading
        animationTimer = setTimeout(() => {
            startAutoSlide();
        }, 2000); // Increased delay to allow content to load first

        return () => {
            if (animationTimer) clearTimeout(animationTimer);
            if (autoScrollTimer) clearTimeout(autoScrollTimer);
        };
    }, [currentBannerIndex, bannerImages.length]);

    // Handle viewable items change for more reliable index tracking
    const onViewableItemsChanged = useRef(({ viewableItems }) => {
        if (viewableItems.length > 0) {
            const newIndex = viewableItems[0].index;
            if (newIndex !== undefined && newIndex !== currentBannerIndex) {
                setCurrentBannerIndex(newIndex);
            }
        }
    }).current;

    // Handle manual scroll end with improved snap behavior
    const handleScrollEnd = (event) => {
        const contentOffset = event.nativeEvent.contentOffset.x;
        const index = Math.round(contentOffset / itemWidth);

        // Ensure the index is valid
        if (index >= 0 && index < bannerImages.length && index !== currentBannerIndex) {
            setCurrentBannerIndex(index);

            // Force snap to the exact position to prevent getting stuck between slides
            if (flatListRef.current) {
                flatListRef.current.scrollToIndex({
                    index,
                    animated: true,
                    viewPosition: 0
                });
            }
        }
    };



    return (
        <View className="w-full px-4 mb-3">
            {/* Modern banner container with enhanced styling */}
            <Animated.View
                className="overflow-hidden rounded-2xl"
                style={{
                    height: 82, // Increased height for better visibility
                    borderRadius: 20, // More rounded corners
                    shadowColor: "#000",
                    shadowOffset: { width: 0, height: 4 },
                    shadowOpacity: 0.15,
                    shadowRadius: 8,
                    elevation: 5,
                    backgroundColor: '#FCF7F8', // Snow color from your theme
                    transform: [{ scale: scaleAnim }] // Apply scale animation
                }}
            >
                <FlatList
                    ref={flatListRef}
                    data={bannerImages}
                    horizontal
                    pagingEnabled
                    showsHorizontalScrollIndicator={false}
                    onScroll={Animated.event(
                        [{ nativeEvent: { contentOffset: { x: scrollX } } }],
                        { useNativeDriver: false }
                    )}
                    snapToInterval={itemWidth}
                    snapToAlignment="center"
                    decelerationRate={0.8} // Slightly slower deceleration for smoother stops
                    onMomentumScrollEnd={handleScrollEnd}
                    onViewableItemsChanged={onViewableItemsChanged}
                    viewabilityConfig={viewConfigRef.current}
                    keyExtractor={(_, index) => `banner-${index}`}
                    getItemLayout={(_, index) => ({
                        length: itemWidth,
                        offset: itemWidth * index,
                        index
                    })}
                    onScrollToIndexFailed={(info) => {
                        // More reliable scroll recovery
                        const wait = new Promise(resolve => setTimeout(resolve, 300));
                        wait.then(() => {
                            if (flatListRef.current) {
                                flatListRef.current.scrollToOffset({
                                    offset: info.index * itemWidth,
                                    animated: false
                                });

                                // Then try to scroll to the index again
                                setTimeout(() => {
                                    if (flatListRef.current) {
                                        flatListRef.current.scrollToIndex({
                                            index: info.index,
                                            animated: false
                                        });
                                    }
                                }, 100);
                            }
                        });
                    }}
                    renderItem={({ item, index }) => {
                        // Calculate distance from center for parallax effect
                        const inputRange = [
                            (index - 1) * itemWidth,
                            index * itemWidth,
                            (index + 1) * itemWidth
                        ];

                        // Enhanced parallax effect with smoother transition
                        const translateX = scrollX.interpolate({
                            inputRange,
                            outputRange: [15, 0, -15], // Slightly increased for more noticeable effect
                            extrapolate: 'clamp'
                        });

                        // Add opacity fade for smoother transitions
                        const opacity = scrollX.interpolate({
                            inputRange,
                            outputRange: [0.8, 1, 0.8],
                            extrapolate: 'clamp'
                        });

                        return (
                            <Animated.View
                                style={{
                                    width: itemWidth,
                                    height: '100%',
                                    borderRadius: 20,
                                    overflow: 'hidden',
                                    opacity,
                                    transform: [{ translateX }]
                                }}
                            >
                                <Image
                                    source={item}
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                    }}
                                    resizeMode="cover"
                                />
                            </Animated.View>
                        );
                    }}
                />
            </Animated.View>
        </View>
    );
};

const HomeScreen = ({ navigation }) => {

    // Animation values - removed for static search bar

    // Cart related state and hooks
    const {
        addToCart,
        cartItems,
        removeFromCart,
        updateCartItemQuantity,
        addToCartModal,
        closeAddToCartModal,
        proceedToCart
    } = useCart();
    const [productQuantities, setProductQuantities] = useState({});

    // User related state and hooks
    const { currentUser, addresses, refreshUserData } = useUser();
    const [addressLoading, setAddressLoading] = useState(true);

    // Product data state
    const [categories, setCategories] = useState([]);
    const [flashSaleProducts, setFlashSaleProducts] = useState([]);
    const [bestSellingProducts, setBestSellingProducts] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);

    // State for the next delivery slot
    const [nextDeliverySlot, setNextDeliverySlot] = useState(null);

    // Optimized pull-to-refresh function with cache clearing
    const onRefresh = React.useCallback(async () => {
        setRefreshing(true);
        setAddressLoading(true);

        try {
            // Clear caches to force fresh data
            clearProductCaches();
            clearCategoriesCache();

            // Parallel refresh of user data and product data
            await Promise.allSettled([
                refreshUserData(),
                fetchProducts() // Use the optimized fetch function
            ]);
        } catch (error) {
            console.error('Error during refresh:', error);
        } finally {
            setRefreshing(false);
            setAddressLoading(false);
        }
    }, [refreshUserData]);

    // Initialize quantities from cart items
    useEffect(() => {
        const quantities = {};
        cartItems.forEach(item => {
            quantities[item.id.split('-')[0]] = item.quantity;
        });
        setProductQuantities(quantities);
    }, [cartItems]);

    // Effect to handle user data and address loading (non-blocking)
    useEffect(() => {
        // Load user data in background without blocking UI
        const loadUserData = async () => {
            setAddressLoading(true);
            try {
                // Use setTimeout to make this non-blocking
                setTimeout(async () => {
                    try {
                        await refreshUserData();
                    } catch (error) {
                        console.error('Error refreshing user data:', error);
                    } finally {
                        setAddressLoading(false);
                    }
                }, 100); // Small delay to not block initial render
            } catch (error) {
                console.error('Error setting up user data loading:', error);
                setAddressLoading(false);
            }
        };

        loadUserData();
    }, []);

    // Effect to update address loading state when addresses change
    useEffect(() => {
        if (addresses && addresses.length > 0) {
            setAddressLoading(false);
        }
    }, [addresses]);



    // Function to handle quantity changes
    const handleQuantityChange = (productId, increment) => {
        setProductQuantities(prev => {
            const currentQty = prev[productId] || 1;
            const newQty = currentQty + increment;

            if (newQty >= 1 && newQty <= 10) {
                return { ...prev, [productId]: newQty };
            }
            return prev;
        });
    };

    // Define the renderQuantityControl function here, outside of JSX
    const renderQuantityControl = (item) => (
        <View className="mt-2">
            {!isInCart(item.id) ? (
                <TouchableOpacity
                    className={`${(item.available === false || item.isAvailable === false) ? 'bg-gray-400' : 'bg-madder'} py-2 rounded-lg flex-row justify-center items-center ${showSearchResults ? "px-2.5" : "px-3"}`}
                    activeOpacity={0.6}
                    onPress={(e) => handleAddToCart(item, e)}
                    disabled={item.available === false || item.isAvailable === false}
                >
                    <MaterialIcons name="shopping-cart" size={14} color="white" />
                    <Text className="text-white text-center text-xs font-bold ml-1.5">
                        {(item.available === false || item.isAvailable === false) ? 'UNAVAILABLE' : 'ADD TO CART'}
                    </Text>
                </TouchableOpacity>
            ) : (
                <View className="bg-madder rounded-xl p-1">
                    <View className="flex-row items-center justify-between">
                        <TouchableOpacity
                            onPress={(e) => {
                                e.stopPropagation();
                                const newQuantity = getQuantity(item.id) - 1;

                                // Find the existing cart item
                                const existingCartItem = cartItems.find(cartItem =>
                                    cartItem.id.split('-')[0] === item.id.toString()
                                );

                                if (newQuantity >= 1) {
                                    // Update local state
                                    handleQuantityChange(item.id, -1);

                                    // Update cart context
                                    if (existingCartItem) {
                                        // Use updateCartItemQuantity instead of addToCart for decrementing
                                        updateCartItemQuantity(existingCartItem.id, newQuantity);
                                    }
                                } else {
                                    // Remove from local state
                                    setProductQuantities(prev => {
                                        const newQuantities = { ...prev };
                                        delete newQuantities[item.id];
                                        return newQuantities;
                                    });

                                    // Remove from cart
                                    if (existingCartItem) {
                                        removeFromCart(existingCartItem.id);
                                    }
                                }
                            }}
                            className="w-8 h-8 rounded-lg bg-snow items-center justify-center shadow-sm"
                        >
                            <MaterialIcons name="remove" size={18} color="black" />
                        </TouchableOpacity>
                        <Text className="w-8 text-center font-bold text-base text-white">{getQuantity(item.id)}</Text>
                        <TouchableOpacity
                            onPress={(e) => {
                                e.stopPropagation();
                                const newQuantity = getQuantity(item.id) + 1;

                                if (newQuantity <= 10) {
                                    // Find the existing cart item
                                    const existingCartItem = cartItems.find(cartItem =>
                                        cartItem.id.split('-')[0] === item.id.toString()
                                    );

                                    // Update local state
                                    handleQuantityChange(item.id, 1);

                                    // Update cart context
                                    if (existingCartItem) {
                                        // Use updateCartItemQuantity for consistency
                                        updateCartItemQuantity(existingCartItem.id, newQuantity);
                                    }
                                }
                            }}
                            className="w-8 h-8 rounded-lg bg-snow items-center justify-center shadow-sm"
                        >
                            <MaterialIcons name="add" size={18} color="black" />
                        </TouchableOpacity>
                    </View>
                </View>
            )}
        </View>
    );

    // Get quantity for a product (default to 1)
    const getQuantity = (productId) => {
        // If item is not in cart, return 1 (default)
        if (!isInCart(productId)) {
            return 1;
        }
        // If item is in cart, get quantity from cart or local state
        const cartItem = cartItems.find(item => item.id.split('-')[0] === productId.toString());
        return cartItem ? cartItem.quantity : (productQuantities[productId] || 1);
    };

    // Check if product is in cart
    const isInCart = (productId) => {
        return cartItems.some(item => item.id.split('-')[0] === productId.toString());
    };

    // Handle add to cart
    const handleAddToCart = (product, event) => {
        event.stopPropagation();

        const quantity = getQuantity(product.id);
        const price = product.discount_price || product.price;
        const cartItem = {
            id: `${product.id}-${product.weight || '500g'}-${Date.now()}`,
            ...product,
            selectedWeight: product.weight || '500g',
            quantity,
            totalPrice: price * quantity,
            savings: product.discount_price ? (product.price - product.discount_price) * quantity : 0
        };

        addToCart(cartItem, navigation);
    };

    // Handle navigation to address screen
    const handleAddressPress = () => {
        navigation.navigate('AddressScreen');
    };

    // Helper function to get combined addresses (including main address)
    const getCombinedAddresses = () => {
        if (!currentUser) return [];

        // Ensure addresses is an array even if it's null or undefined
        const addressesArray = Array.isArray(addresses) ? addresses : [];
        const combinedAddresses = [...addressesArray];

        // Check if the user has a main address that's not already in the addresses array
        if (currentUser.address &&
            (currentUser.address.doorNo || currentUser.address.streetName ||
             currentUser.address.area || currentUser.address.district)) {

            // Check if this address is already in the array
            const isMainAddressDuplicate = addressesArray.some(addr =>
                addr.doorNo === currentUser.address.doorNo &&
                addr.streetName === currentUser.address.streetName &&
                addr.area === currentUser.address.area &&
                addr.district === currentUser.address.district &&
                addr.pincode === currentUser.address.pincode
            );

            if (!isMainAddressDuplicate) {
                // Add the main address to the combined list
                const mainAddress = {
                    _id: 'main-address',
                    type: currentUser.address.addressType || 'Home',
                    doorNo: currentUser.address.doorNo || '',
                    streetName: currentUser.address.streetName || '',
                    area: currentUser.address.area || '',
                    district: currentUser.address.district || '',
                    pincode: currentUser.address.pincode || '',
                    fullAddress: currentUser.address.fullAddress ||
                        `${currentUser.address.doorNo || ''}, ${currentUser.address.streetName || ''}, ${currentUser.address.area || ''}, ${currentUser.address.district || ''}, ${currentUser.address.pincode || ''}`,
                    isMain: true
                };

                combinedAddresses.push(mainAddress);
            }
        }

        return combinedAddresses;
    };

    // Handle navigation to profile screen
    const handleProfilePress = () => {
        navigation.navigate('UserProfileScreen');
    };

    // Optimized fetch products with progressive loading and caching
    const fetchProducts = async () => {
        try {
            setIsLoading(true);

            // Step 1: Load categories first (fastest API call)
            const categoriesResponse = await getAllCategories().catch(err => {
                console.error('Categories failed:', err);
                return [];
            });

            // Process and display categories immediately
            let categoriesData = [];
            if (Array.isArray(categoriesResponse)) {
                categoriesData = categoriesResponse.map(category => ({
                    id: category._id,
                    name: category.name,
                    image: category.image,
                    products: []
                }));
                setCategories(categoriesData);
                setIsLoading(false); // Show categories immediately
            }

            // Step 2: Load products and bestselling in parallel (background)
            const [productsResponse, bestsellingResponse] = await Promise.allSettled([
                getUserProducts(),
                getBestsellingProducts()
            ]);

            // Process products in background
            if (productsResponse.status === 'fulfilled' && productsResponse.value?.products) {
                const products = productsResponse.value.products;

                // Group products by category efficiently
                if (categoriesData.length > 0) {
                    const categoryMap = new Map(categoriesData.map(cat => [cat.id, cat]));

                    products.forEach(product => {
                        if (product.category) {
                            const categoryId = product.category._id || product.category;
                            const category = categoryMap.get(categoryId);
                            if (category) {
                                category.products.push(product);
                            }
                        }
                    });
                } else {
                    // Fallback: Create categories from products
                    const categoriesMap = new Map();

                    products.forEach(product => {
                        if (product.category) {
                            const categoryId = product.category._id || product.category;
                            const categoryName = product.category.name || 'Uncategorized';

                            if (!categoriesMap.has(categoryId)) {
                                categoriesMap.set(categoryId, {
                                    id: categoryId,
                                    name: categoryName,
                                    image: product.category.image || require('../assets/logo.png'),
                                    products: []
                                });
                            }
                            categoriesMap.get(categoryId).products.push(product);
                        }
                    });

                    categoriesData = Array.from(categoriesMap.values());
                }

                setCategories(categoriesData);

                // Process bestselling products
                let bestSellingProducts = [];
                if (bestsellingResponse.status === 'fulfilled' && bestsellingResponse.value?.products) {
                    bestSellingProducts = bestsellingResponse.value.products;
                } else {
                    // Fallback to first 10 products
                    bestSellingProducts = products.slice(0, 10);
                }

                setBestSellingProducts(bestSellingProducts);

                // Generate flash sale products only once
                if (flashSaleProducts.length === 0) {
                    generateRandomFlashSaleProducts(products, bestSellingProducts);
                }
            }
        } catch (error) {
            console.error('Error fetching products:', error);
            setCategories([]);
            setFlashSaleProducts([]);
            setBestSellingProducts([]);
            setIsLoading(false);
        }
    };

    // Fetch products when component mounts
    useEffect(() => {
        fetchProducts();
        calculateNextDeliverySlot();
    }, []);

    // Clean up local quantity state when items are removed from cart
    useEffect(() => {
        setProductQuantities(prev => {
            const newQuantities = { ...prev };
            let hasChanges = false;

            // Remove quantities for products that are no longer in cart
            Object.keys(newQuantities).forEach(productId => {
                if (!isInCart(productId)) {
                    delete newQuantities[productId];
                    hasChanges = true;
                }
            });

            return hasChanges ? newQuantities : prev;
        });

        // Close add to cart modal when cart becomes empty
        if (cartItems.length === 0 && addToCartModal.visible) {
            closeAddToCartModal();
        }
    }, [cartItems, addToCartModal.visible]);

    // Function to generate random flash sale products
    const generateRandomFlashSaleProducts = (allProducts, bestSellingProducts) => {
        // Filter out best selling products to avoid duplicates
        const availableProducts = allProducts.filter(product =>
            !bestSellingProducts.some(bestProduct => bestProduct._id === product._id)
        );

        // Shuffle the available products
        const shuffledProducts = [...availableProducts].sort(() => 0.5 - Math.random());

        // Take 6-7 random products
        const count = Math.floor(Math.random() * 2) + 6; // Random number between 6 and 7
        const randomProducts = shuffledProducts.slice(0, count);

        // Update flash sale products
        setFlashSaleProducts(randomProducts);
    };

    // Calculate the next available delivery slot
    const calculateNextDeliverySlot = (timerEnded = false) => {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();

        // Base slot definitions (same as in CheckoutScreen)
        const BASE_DELIVERY_SLOTS = [
            { id: 1, time: "9:00 AM - 12:00 PM", label: "Morning", day: "Today", startHour: 9, endHour: 12 },
            { id: 2, time: "5:00 PM - 7:00 PM", label: "Evening", day: "Today", startHour: 17, endHour: 19 },
            { id: 3, time: "9:00 AM - 12:00 PM", label: "Morning", day: "Tomorrow", startHour: 9, endHour: 12 },
            { id: 4, time: "5:00 PM - 7:00 PM", label: "Evening", day: "Tomorrow", startHour: 17, endHour: 19 },
        ];

        // Calculate cutoff time (need at least 1 hour before slot starts)
        const CUTOFF_HOURS = 1;

        // Filter available slots
        const availableSlots = BASE_DELIVERY_SLOTS.filter(slot => {
            if (slot.day === "Tomorrow") {
                // All tomorrow slots are available
                return true;
            }

            // For today's slots, check if we're past the cutoff time
            if (slot.day === "Today") {
                // If current time + cutoff is past the slot start time, it's unavailable
                if (currentHour > slot.startHour - CUTOFF_HOURS) {
                    return false;
                }

                // If we're exactly at the cutoff hour, check minutes
                if (currentHour === slot.startHour - CUTOFF_HOURS && currentMinute > 0) {
                    return false;
                }
            }

            return true;
        });

        // Set the next available slot
        if (availableSlots.length > 0) {
            setNextDeliverySlot(availableSlots[0]);

            // If timer ended, generate new random flash sale products
            if (timerEnded) {
                // Get all products and generate random flash sale products
                getUserProducts()
                    .then(response => {
                        if (response.success) {
                            generateRandomFlashSaleProducts(response.products, bestSellingProducts);
                        }
                    })
                    .catch(error => {
                        console.error("Error fetching products for flash sale:", error);
                    });
            }
        }
    };

    // Calculate the next available delivery slot when component mounts and update periodically
    useEffect(() => {
        calculateNextDeliverySlot();

        // Update delivery slot every minute to ensure accuracy
        const slotUpdateInterval = setInterval(() => {
            calculateNextDeliverySlot();
        }, 60000); // Update every minute

        return () => {
            clearInterval(slotUpdateInterval);
        };
    }, []);

    const [search, setSearch] = useState("");
    const [filteredItems, setFilteredItems] = useState([]);
    const [searchResult, setSearchResult] = useState(null);
    const [showSearchResults, setShowSearchResults] = useState(false);

    const updateSearch = (searchText) => {
        setSearch(searchText);
        handleSearch(searchText);
    };

    const handleSearch = (searchText) => {
        if (searchText) {
            // Get all products from all categories
            const allItems = categories.flatMap(category => category.products);
            const filtered = allItems.filter(item => item && item.name.toLowerCase().includes(searchText.toLowerCase()));
            setFilteredItems(filtered);
            setSearchResult(filtered.length > 0 ? filtered : "Product Not Found");
            setShowSearchResults(true);
        } else {
            setFilteredItems([]);
            setSearchResult(null);
            setShowSearchResults(false);
        }
    };

    const handleCategoryClick = (category) => {
        console.log('Navigating to category:', category);
        navigation.navigate('CategoryScreen', { category });
    };

    const handleProductClick = (product) => {
        navigation.navigate('ProductDetailScreen', { product });
    };

    // Remove the automatic scrolling effect
    useEffect(() => {
        // Removing the timer that was causing automatic scrolling
        return () => { };
    }, []);

    const renderContent = () => {
        if (showSearchResults && searchResult) {
            return searchResult === "Product Not Found" ? (
                <Text className="text-center text-red-500 text-lg font-medium my-4">{searchResult}</Text>
            ) : (
                <FlatList className="p-6 -mt-4"
                    data={filteredItems}
                    keyExtractor={(item) => item ? item.id : Math.random().toString()}
                    showsVerticalScrollIndicator={false}
                    renderItem={({ item }) => item && (
                        <TouchableOpacity
                            onPress={() => handleProductClick(item)}
                            activeOpacity={0.9}
                            className="mb-4"
                        >
                            <View className="bg-white rounded-xl overflow-hidden shadow-sm"
                                style={{
                                    shadowColor: '#000', shadowOffset: { width: 0, height: 2 },
                                    shadowOpacity: 0.1, shadowRadius: 3, elevation: 3
                                }}>
                                <View className="h-48">
                                    {item.image && (
                                        <View className="relative w-full h-full">
                                            <Image
                                                source={typeof item.image === 'string' ? { uri: item.image } : item.image}
                                                className="w-full h-full"
                                                resizeMode="cover"
                                            />

                                            {/* Unavailable overlay */}
                                            {(item.available === false || item.isAvailable === false) && (
                                                <View className="absolute inset-0 bg-black/70 flex items-center justify-center">
                                                    <View className="bg-black/80 px-4 py-2 rounded-lg">
                                                        <Text className="text-white font-bold text-lg">UNAVAILABLE</Text>
                                                    </View>
                                                </View>
                                            )}

                                            {item.discount_price && (
                                                <View className="absolute top-2 right-2 bg-madder px-2 py-1 rounded-full">
                                                    <Text className="text-white text-xs font-bold">
                                                        {Math.round(((item.price - (item.discount_price || Math.round(item.price * 0.7))) / item.price) * 100)}% OFF
                                                    </Text>
                                                </View>
                                            )}
                                        </View>
                                    )}
                                </View>

                                <View className="p-4">
                                    <Text className="text-lg font-bold text-gray-800" numberOfLines={1} ellipsizeMode="tail">
                                        {item.name}
                                    </Text>
                                    <Text className="text-sm text-gray-500 mt-1 mb-2" numberOfLines={2} ellipsizeMode="tail">
                                        {item.description}
                                    </Text>

                                    <View className="bg-gray-100 self-start px-3 py-1 rounded-full mb-2">
                                        <Text className="text-sm font-medium text-gray-700">
                                            {item.weight}
                                        </Text>
                                    </View>

                                    <View className="flex-row justify-between items-center mt-1">
                                        <View className="flex-row items-center">
                                            {item.discount_price ? (
                                                <>
                                                    <Text className="text-madder text-lg font-bold mr-2">₹{item.discount_price}</Text>
                                                    <Text className="text-gray-400 text-sm line-through">₹{item.price}</Text>
                                                </>
                                            ) : (
                                                <Text className="text-madder text-lg font-bold">₹{item.price}</Text>
                                            )}
                                        </View>

                                        {/* Replace Buy button with Add to Cart */}
                                        {renderQuantityControl(item)}
                                    </View>
                                </View>
                            </View>
                        </TouchableOpacity>
                    )}
                />
            );
        } else {
            return (
                <>
                    <View className="-mt-4 mb-2">
                        <BannerCarousel />
                    </View>

                    {/* Category Section */}
                    <View className="mt-4">
                        <Text className="text-lg font-semibold ml-6">Category</Text>
                        {isLoading ? (
                            // Skeleton loading for categories
                            <FlatList
                                className="ml-2 mr-2 mt-1"
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                data={[1, 2, 3, 4, 5]}
                                keyExtractor={(item) => `skeleton-category-${item}`}
                                renderItem={() => <CategorySkeleton />}
                            />
                        ) : (
                            <FlatList className="ml-2 mr-2 mt-1"
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                data={categories}
                                keyExtractor={(item) => item.id}
                                initialNumToRender={10}
                                maxToRenderPerBatch={10}
                                windowSize={5}
                                removeClippedSubviews={true}
                                renderItem={({ item }) => (
                                    <TouchableOpacity
                                        className="items-center mx-3 mt-2"
                                        onPress={() => handleCategoryClick(item)}
                                        activeOpacity={0.8}
                                    >
                                        <View className="bg-white rounded-full shadow-md" style={{ shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.2, shadowRadius: 1.5, elevation: 2 }}>
                                            <Image
                                                source={typeof item.image === 'string' ? { uri: item.image } : item.image}
                                                className="w-20 h-20 rounded-full"
                                                resizeMode="contain"
                                            />
                                        </View>
                                        <Text className="text-sm mt-2 font-medium">{item.name}</Text>
                                    </TouchableOpacity>
                                )}
                            />
                        )}
                    </View>

                    {/* Today's Deals Section with Anime-Style Timer */}
                    <View className="mt-4">
                        {/* Title and timer side by side */}
                        <View className="flex-row items-center mx-6 mb-2">
                            <View className="flex-row items-center mr-3">
                                <Text className="text-xl font-bold mr-1 text-madder">Fresh</Text>
                                <Text className="text-xl font-bold">Deals</Text>
                            </View>

                            <FlashSaleTimer
                                deliverySlot={nextDeliverySlot}
                                onTimerEnd={() => {
                                    console.log("Timer ended, refreshing Fresh Deals products");
                                    calculateNextDeliverySlot(true);
                                }}
                                showDeliveryInfo={false}
                            />
                        </View>

                        {/* Enhanced delivery info displayed separately */}
                        <View className="mx-6 mb-3">
                            {nextDeliverySlot && (
                                <View className="flex-row items-center">
                                    <MaterialIcons name="delivery-dining" size={14} color="#A31621" />
                                    <Text className="text-gray-600 text-xs ml-1">
                                        Order before for{' '}
                                        <Text className="font-medium text-madder">{nextDeliverySlot.day.toLowerCase()}</Text> delivery at{' '}
                                        <Text className="font-medium text-madder">{nextDeliverySlot.time}</Text>{' '}
                                        <Text className="font-medium">({nextDeliverySlot.label})</Text>
                                    </Text>
                                </View>
                            )}
                        </View>



                        {isLoading ? (
                            // Skeleton loading for flash sale products
                            <FlatList
                                className="ml-2 mr-2"
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                data={[1, 2, 3, 4]}
                                keyExtractor={(item) => `skeleton-flash-${item}`}
                                renderItem={() => <ProductCardSkeleton width={176} />}
                            />
                        ) : (
                            <FlatList
                                className="ml-2 mr-2"
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                data={flashSaleProducts}
                                keyExtractor={(item) => `flash-${item.id}`}
                                initialNumToRender={5}
                                maxToRenderPerBatch={5}
                                windowSize={5}
                                removeClippedSubviews={true}
                                renderItem={({ item }) => (
                                    <TouchableOpacity
                                        onPress={() => handleProductClick(item)}
                                        activeOpacity={0.9}
                                    >
                                        <View
                                            className="w-44 bg-white rounded-xl m-2 overflow-hidden shadow-sm"
                                            style={{
                                                shadowColor: "#000",
                                                shadowOffset: { width: 0, height: 2 },
                                                shadowOpacity: 0.1,
                                                shadowRadius: 3,
                                                elevation: 3,
                                            }}
                                        >
                                            <View className="relative">
                                                {item.image && (
                                                    <View className="relative">
                                                        <Image
                                                            source={typeof item.image === 'string' ? { uri: item.image } : item.image}
                                                            className="w-full h-28"
                                                            resizeMode="cover"
                                                        />

                                                        {/* Unavailable overlay */}
                                                        {(item.available === false || item.isAvailable === false) && (
                                                            <View className="absolute inset-0 bg-black/70 flex items-center justify-center">
                                                                <Text className="text-white font-bold text-sm">UNAVAILABLE</Text>
                                                            </View>
                                                        )}
                                                    </View>
                                                )}
                                                <View className="absolute top-2 right-2 bg-madder px-2 py-1 rounded-full">
                                                    <Text className="text-white text-xs font-bold">
                                                        {Math.round(((item.price - item.discount_price) / item.price) * 100)}% OFF
                                                    </Text>
                                                </View>
                                                <View className="absolute bottom-2 left-2 bg-black/70 px-2 py-1 rounded-md">
                                                    <Text className="text-white text-xs font-medium">
                                                        Limited Stock
                                                    </Text>
                                                </View>
                                            </View>

                                            <View className="p-3">
                                                <Text className="text-sm font-bold" numberOfLines={1}>{item.name}</Text>
                                                <View className="bg-gray-100 self-start px-2 py-0.5 rounded-full my-1">
                                                    <Text className="text-xs text-gray-700">{item.weight}</Text>
                                                </View>
                                                <View className="flex-row items-center mt-1">
                                                    <Text className="text-madder text-sm font-bold">₹{item.discount_price}</Text>
                                                    <Text className="text-gray-400 text-xs line-through ml-2">₹{item.price}</Text>
                                                </View>
                                                {renderQuantityControl(item)}
                                            </View>
                                        </View>
                                    </TouchableOpacity>
                                )}
                            />
                        )}
                    </View>

                    {/* Best Selling Section with Catchier Title */}
                    <View className="mt-2">
                        <View className="flex-row items-center mx-6 mb-2">
                            <View className="flex-row items-center">
                                <Text className="text-xl font-bold mr-1 text-madder">Top</Text>
                                <Text className="text-xl font-bold">Picks</Text>
                            </View>
                        </View>

                        {isLoading ? (
                            // Skeleton loading for best selling products
                            <FlatList
                                className="ml-2 mr-2"
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                data={[1, 2, 3, 4]}
                                keyExtractor={(item) => `skeleton-top-pick-${item}`}
                                renderItem={() => <ProductCardSkeleton width={176} />}
                            />
                        ) : (
                            <FlatList
                                className="ml-2 mr-2"
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                data={bestSellingProducts}
                                keyExtractor={(item) => `top-pick-${item.id}`}
                                initialNumToRender={5}
                                maxToRenderPerBatch={5}
                                windowSize={5}
                                removeClippedSubviews={true}
                                renderItem={({ item }) => (
                                    <TouchableOpacity
                                        onPress={() => handleProductClick(item)}
                                        activeOpacity={0.9}
                                    >
                                        <View
                                            className="w-44 bg-white rounded-xl m-2 overflow-hidden shadow-sm"
                                            style={{
                                                shadowColor: "#000",
                                                shadowOffset: { width: 0, height: 2 },
                                                shadowOpacity: 0.1,
                                                shadowRadius: 3,
                                                elevation: 3,
                                            }}
                                        >
                                            <View className="relative">
                                                {item.image && (
                                                    <View className="relative">
                                                        <Image
                                                            source={typeof item.image === 'string' ? { uri: item.image } : item.image}
                                                            className="w-full h-28"
                                                            resizeMode="cover"
                                                        />

                                                        {/* Unavailable overlay */}
                                                        {(item.available === false || item.isAvailable === false) && (
                                                            <View className="absolute inset-0 bg-black/70 flex items-center justify-center">
                                                                <Text className="text-white font-bold text-sm">UNAVAILABLE</Text>
                                                            </View>
                                                        )}
                                                    </View>
                                                )}
                                                {item.discount_price && (
                                                    <View className="absolute top-2 right-2 bg-madder px-2 py-1 rounded-full">
                                                        <Text className="text-white text-xs font-bold">
                                                            {Math.round(((item.price - (item.discount_price || Math.round(item.price * 0.7))) / item.price) * 100)}% OFF
                                                        </Text>
                                                    </View>
                                                )}
                                            </View>

                                            <View className="p-3">
                                                <Text className="text-sm font-bold" numberOfLines={1}>{item.name}</Text>
                                                <View className="bg-gray-100 self-start px-2 py-0.5 rounded-full my-1">
                                                    <Text className="text-xs text-gray-700">{item.weight}</Text>
                                                </View>
                                                <View className="flex-row items-center mt-1">
                                                    {item.discount_price ? (
                                                        <>
                                                            <Text className="text-madder text-sm font-bold">₹{item.discount_price}</Text>
                                                            <Text className="text-gray-400 text-xs line-through ml-2">₹{item.price}</Text>
                                                        </>
                                                    ) : (
                                                        <Text className="text-madder text-sm font-bold">₹{item.price}</Text>
                                                    )}
                                                </View>
                                                {renderQuantityControl(item)}
                                            </View>
                                        </View>
                                    </TouchableOpacity>
                                )}
                            />
                        )}
                    </View>
                </>
            );
        }
    };


    return (
        <View className="flex-1 bg-snow">
            <StatusBar backgroundColor="#A31621" barStyle="light-content" translucent={false} />

            {/* We've removed the loading overlay in favor of skeleton loading in the content */}

            <View className="bg-madder flex-row h-28 rounded-b-3xl p-6 justify-between items-center">
                <View className="flex-1 mr-3">
                    <Text className="text-white text-xl font-bold" numberOfLines={1}>
                        {currentUser?.name ? `Hi, ${currentUser.name.split(' ')[0]}` : 'Welcome'}
                    </Text>
                    <TouchableOpacity
                        className="flex-row items-center mt-0.5"
                        onPress={handleAddressPress}
                        activeOpacity={0.7}
                    >
                        <View className="flex-row items-center flex-1">
                            <MaterialIcons name="location-on" size={14} color="white" />
                            <View className="flex-1 ml-1">
                                {/* Show loading indicator or address */}
                                {addressLoading ? (
                                    <View className="flex-row items-center">
                                        <Text className="text-white text-[13px] mr-2" numberOfLines={1}>
                                            Loading address...
                                        </Text>
                                        <View className="h-2 w-2 rounded-full bg-white/70 animate-pulse" />
                                    </View>
                                ) : (
                                    (() => {
                                        const combinedAddrs = getCombinedAddresses();
                                        const primaryAddress = combinedAddrs.find(addr => addr.isPrimary);
                                        const mainAddress = combinedAddrs.find(addr => addr.isMain);
                                        const defaultAddress = combinedAddrs.find(addr => addr.isDefault) || combinedAddrs[0];

                                        if (primaryAddress) {
                                            return (
                                                <Text className="text-white text-[13px]" numberOfLines={1}>
                                                    {primaryAddress.fullAddress ||
                                                    `${primaryAddress.doorNo || ''}${primaryAddress.streetName ? `, ${primaryAddress.streetName}` : ''}${primaryAddress.area ? `, ${primaryAddress.area}` : ''}${primaryAddress.district ? `, ${primaryAddress.district}` : ''}${primaryAddress.pincode ? ` - ${primaryAddress.pincode}` : ''}`}
                                                </Text>
                                            );
                                        } else if (mainAddress) {
                                            return (
                                                <Text className="text-white text-[13px]" numberOfLines={1}>
                                                    {mainAddress.fullAddress ||
                                                    `${mainAddress.doorNo || ''}${mainAddress.streetName ? `, ${mainAddress.streetName}` : ''}${mainAddress.area ? `, ${mainAddress.area}` : ''}${mainAddress.district ? `, ${mainAddress.district}` : ''}${mainAddress.pincode ? ` - ${mainAddress.pincode}` : ''}`}
                                                </Text>
                                            );
                                        } else if (defaultAddress) {
                                            return (
                                                <Text className="text-white text-[13px]" numberOfLines={1}>
                                                    {defaultAddress.fullAddress ||
                                                    `${defaultAddress.doorNo || ''}${defaultAddress.streetName ? `, ${defaultAddress.streetName}` : ''}${defaultAddress.area ? `, ${defaultAddress.area}` : ''}${defaultAddress.district ? `, ${defaultAddress.district}` : ''}${defaultAddress.pincode ? ` - ${defaultAddress.pincode}` : ''}`}
                                                </Text>
                                            );
                                        } else {
                                            return (
                                                <Text className="text-white text-[13px]" numberOfLines={1}>
                                                    Add your address
                                                </Text>
                                            );
                                        }
                                    })()
                                )}
                            </View>
                        </View>
                        <MaterialIcons name="keyboard-arrow-right" size={16} color="white" />
                    </TouchableOpacity>
                </View>
                <TouchableOpacity
                    onPress={handleProfilePress}
                    className="bg-#A31621 rounded-full overflow-hidden p-1 shadow-md"
                    style={{
                        shadowColor: "#A31621",
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.2,
                        shadowRadius: 3,
                        elevation: 4,
                    }}
                >
                    <Image
                        source={logo}
                        className="w-16 h-16 rounded-full"
                        style={{ borderWidth: 1, borderColor: '#FCF7F8' }}
                    />
                </TouchableOpacity>
            </View>
            <FlatList
                ListHeaderComponent={
                    <View className="p-6 pt-4 mb-2">
                        <AnimatedSearchBar
                            key="animated-search-bar"
                            value={search}
                            onChangeText={updateSearch}
                            onSubmitEditing={() => {
                                handleSearch(search);
                                Keyboard.dismiss();
                            }}
                            onClear={() => {
                                setSearch("");
                                setShowSearchResults(false);
                            }}
                            placeholders={["Chicken", "Mutton", "Fish", "Prawn", "Crabs", "Eggs"]}
                            animationDuration={4000}
                            containerStyle={{ marginBottom: 10 }}
                        />
                    </View>
                }
                data={[]}
                renderItem={null}
                ListFooterComponent={renderContent}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 20 }} // Reduced padding for tab bar
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={onRefresh}
                        colors={["#A31621"]}
                        tintColor="#A31621"
                        title="Refreshing data from database..."
                        titleColor="#A31621"
                    />
                }
            />

            {/* Add to Cart Modal */}
            <AddToCartModal
                visible={addToCartModal.visible}
                onClose={closeAddToCartModal}
                onProceedToCart={proceedToCart}
                productName={addToCartModal.productName}
                productWeight={addToCartModal.productWeight}
                isUpdate={addToCartModal.isUpdate}
            />
        </View>
    );
};

export default HomeScreen;