import React, { useState, useEffect, useRef } from 'react';
import { View, Text, FlatList, Image, TouchableOpacity, StatusBar, ActivityIndicator, Animated } from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { getUserProductsByCategory } from '../utils/api/userProductApi';
import { useCart } from '../context/CartContext';
import AddToCartModal from '../Components/AddToCartModal';
import RemovedFromCartModal from '../Components/RemovedFromCartModal';

// Skeleton loading components
const SkeletonBox = ({ width, height, style }) => {
    const pulseAnim = useRef(new Animated.Value(0.3)).current;

    useEffect(() => {
        const pulse = Animated.loop(
            Animated.sequence([
                Animated.timing(pulseAnim, {
                    toValue: 1,
                    duration: 800,
                    useNativeDriver: true
                }),
                Animated.timing(pulseAnim, {
                    toValue: 0.3,
                    duration: 800,
                    useNativeDriver: true
                })
            ])
        );
        pulse.start();

        return () => pulse.stop();
    }, []);

    return (
        <Animated.View
            style={[
                {
                    width: width,
                    height: height,
                    backgroundColor: '#E0E0E0',
                    borderRadius: 8,
                    opacity: pulseAnim
                },
                style
            ]}
        />
    );
};

const ProductCardSkeleton = () => {
    return (
        <View className="mb-4">
            <View className="bg-white rounded-xl overflow-hidden shadow-sm">
                <SkeletonBox width="100%" height={200} />
                <View className="p-4">
                    <SkeletonBox width="80%" height={20} style={{ marginBottom: 8 }} />
                    <SkeletonBox width="100%" height={16} style={{ marginBottom: 8 }} />
                    <SkeletonBox width="100%" height={16} style={{ marginBottom: 16 }} />
                    <SkeletonBox width={80} height={24} style={{ marginBottom: 16, borderRadius: 20 }} />
                    <View className="flex-row justify-between items-center">
                        <SkeletonBox width={80} height={24} />
                        <SkeletonBox width={80} height={36} style={{ borderRadius: 8 }} />
                    </View>
                </View>
            </View>
        </View>
    );
};

const CategoryScreen = ({ route, navigation }) => {
    const { category } = route.params;
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [error, setError] = useState(null);
    const {
        addToCart,
        cartItems,
        removeFromCart,
        updateCartItemQuantity,
        addToCartModal,
        closeAddToCartModal,
        removedFromCartModal,
        closeRemovedFromCartModal,
        proceedToCart
    } = useCart();

    // Function to fetch products for this category
    const fetchCategoryProducts = async () => {
        try {
            setLoading(true);
            setError(null);

            // Get the category ID, handling different formats from different screens
            const categoryId = category._id || category.id;

            if (!categoryId) {
                console.error('Invalid category ID:', category);
                setError('Invalid category ID. Please try again.');
                setProducts([]);
                setLoading(false);
                setRefreshing(false);
                return;
            }

            console.log('Fetching products for category ID:', categoryId);
            console.log('Category object:', JSON.stringify(category, null, 2));

            const response = await getUserProductsByCategory(categoryId);
            console.log('Products fetched for category:', response);

            if (response && response.products) {
                setProducts(response.products);
            } else {
                console.log('No products found for this category');
                setProducts([]);
            }
        } catch (error) {
            console.error(`Error fetching category products:`, error);
            setError('Failed to load products. Please try again.');

            // Try to get products from the category object if API fails
            if (category.products && Array.isArray(category.products) && category.products.length > 0) {
                console.log('Using products from category object as fallback');
                setProducts(category.products);
                setError(null); // Clear error if we have fallback products
            } else {
                setProducts([]);
            }
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    // Fetch products when component mounts
    useEffect(() => {
        fetchCategoryProducts();
    }, [category._id, category.id]);

    // Handle refresh
    const handleRefresh = () => {
        setRefreshing(true);
        fetchCategoryProducts();
    };

    return (
        <View className="flex-1 bg-gray-50">
            <StatusBar barStyle="light-content" backgroundColor="#A31621" />

            {/* Improved Header */}
            <View className="bg-madder h-24 rounded-b-3xl flex-row items-end justify-between px-4 pb-4">
                <View className="flex-row items-center">
                    <TouchableOpacity
                        className="p-2 rounded-full"
                        onPress={() => navigation.goBack()}
                    >
                        <Ionicons name="arrow-back" size={24} color="white" />
                    </TouchableOpacity>
                    <Text className="text-2xl text-white font-bold ml-3">{category.name}</Text>
                </View>

                <TouchableOpacity
                    className="p-2 rounded-full"
                    onPress={() => navigation.navigate('MainTabs', { screen: 'Cart' })}
                >
                    <Ionicons name="cart-outline" size={24} color="white" />
                </TouchableOpacity>
            </View>

            {/* Error Message */}
            {error && (
                <View className="p-4 m-4 bg-red-100 rounded-lg">
                    <Text className="text-red-700 text-center">{error}</Text>
                    <TouchableOpacity
                        className="mt-2 bg-madder py-2 rounded-lg"
                        onPress={handleRefresh}
                    >
                        <Text className="text-white text-center font-medium">Try Again</Text>
                    </TouchableOpacity>
                </View>
            )}

            {/* Product List with Skeleton Loading */}
            {loading ? (
                <FlatList
                    className="px-3 pt-3"
                    data={[1, 2, 3, 4, 5]}
                    keyExtractor={(item) => `skeleton-${item}`}
                    showsVerticalScrollIndicator={false}

                    renderItem={() => <ProductCardSkeleton />}
                />
            ) : products.length > 0 ? (
                <FlatList
                    className="px-3 pt-3"
                    data={products}
                    keyExtractor={(item) => (item._id || item.id).toString()}
                    showsVerticalScrollIndicator={false}
                    onRefresh={handleRefresh}
                    refreshing={refreshing}

                    renderItem={({ item }) => (
                    <TouchableOpacity
                        className="mb-4"
                        activeOpacity={0.9}
                        onPress={() => navigation.navigate('ProductDetailScreen', { product: item })}
                    >
                        <View className="bg-white rounded-xl overflow-hidden shadow-sm"
                              style={{ shadowColor: '#000', shadowOffset: { width: 0, height: 2 },
                                      shadowOpacity: 0.1, shadowRadius: 3, elevation: 3 }}>
                            <View className="h-48">
                                {/* Always show an image, using logo as fallback */}
                                    <Image
                                        source={typeof item.image === 'string' ? { uri: item.image } : item.image || require('../assets/logo.png')}
                                        className="w-full h-full"
                                        resizeMode="cover"
                                    />
                                {item.discount_price && (
                                    <View className="absolute top-2 right-2 bg-madder px-2 py-1 rounded-full">
                                        <Text className="text-white text-xs font-bold">
                                            {Math.round((item.price - item.discount_price) / item.price * 100)}% OFF
                                        </Text>
                                    </View>
                                )}
                            </View>

                            <View className="p-4">
                                <Text className="text-lg font-bold text-gray-800" numberOfLines={1} ellipsizeMode="tail">
                                    {item.name}
                                </Text>
                                <Text className="text-sm text-gray-500 mt-1 mb-2" numberOfLines={2} ellipsizeMode="tail">
                                    {item.description}
                                </Text>

                                {/* Enhanced weight display */}
                                <View className="bg-gray-100 self-start px-3 py-1 rounded-full mb-2">
                                    <Text className="text-sm font-medium text-gray-700">
                                        {item.weight}
                                    </Text>
                                </View>

                                <View className="flex-row justify-between items-center mt-1">
                                    <View className="flex-row items-center">
                                        {item.discount_price ? (
                                            <View className="flex-row items-center">
                                                <Text className="text-madder text-lg font-bold mr-2">₹{item.discount_price}</Text>
                                                <Text className="text-gray-400 text-sm line-through">₹{item.price}</Text>
                                            </View>
                                        ) : (
                                            <Text className="text-madder text-lg font-bold">₹{item.price}</Text>
                                        )}
                                    </View>

                                    <TouchableOpacity
                                        className="bg-madder px-4 py-2 rounded-lg flex-row items-center"
                                        onPress={() => navigation.navigate('ProductDetailScreen', { product: item })}
                                    >
                                        <Text className="text-white font-medium mr-1">Buy</Text>
                                        <MaterialIcons name="arrow-forward" size={16} color="white" />
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </TouchableOpacity>
                )}
            />
            ) : (
                <View className="flex-1 items-center justify-center p-4">
                    <MaterialIcons name="category" size={64} color="#A31621" />
                    <Text className="text-xl font-bold text-gray-800 mt-4">No Products Found</Text>
                    <Text className="text-gray-600 text-center mt-2">
                        We couldn't find any products in this category. Please check back later.
                    </Text>
                    <TouchableOpacity
                        className="bg-madder px-4 py-2 rounded-lg mt-4"
                        onPress={handleRefresh}
                    >
                        <Text className="text-white font-medium">Refresh</Text>
                    </TouchableOpacity>
                </View>
            )}

            {/* Add to Cart Modal */}
            <AddToCartModal
                visible={addToCartModal.visible}
                onClose={closeAddToCartModal}
                onProceedToCart={proceedToCart}
                productName={addToCartModal.productName}
                productWeight={addToCartModal.productWeight}
                isUpdate={addToCartModal.isUpdate}
            />

            {/* Removed from Cart Modal */}
            <RemovedFromCartModal
                visible={removedFromCartModal.visible}
                onClose={closeRemovedFromCartModal}
                productName={removedFromCartModal.productName}
                productWeight={removedFromCartModal.productWeight}
            />
        </View>
    );
};

export default CategoryScreen;
